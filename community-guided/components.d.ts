/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AreaList: typeof import('./src/components/AreaList.vue')['default']
    CategoryList: typeof import('./src/components/index/CategoryList.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    PageBack: typeof import('./src/components/common/PageBack.vue')['default']
    PopupTitle: typeof import('./src/components/common/PopupTitle.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchList: typeof import('./src/components/SearchList.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanEmpty: typeof import('vant/es')['Empty']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanIndexAnchor: typeof import('vant/es')['IndexAnchor']
    VanIndexBar: typeof import('vant/es')['IndexBar']
    VanList: typeof import('vant/es')['List']
    VanPopup: typeof import('vant/es')['Popup']
    VanSearch: typeof import('vant/es')['Search']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTextEllipsis: typeof import('vant/es')['TextEllipsis']
  }
}
