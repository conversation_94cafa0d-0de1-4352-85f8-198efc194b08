/* @import './base.css';

@font-face {
  font-family: 'LilitaOne-Regular';
  src: url('./LilitaOne-Regular.ttf') format('woff2');
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  font-weight: normal;

  font-family: "LilitaOne-Regular";
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }
} */