<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import AMapLoader from '@amap/amap-jsapi-loader'
import wx from 'weixin-js-sdk'

import { getAttractionsDetails, getDetails, getSign } from '@/common/api'

import { Carousel, Slide } from 'vue3-carousel'
import 'vue3-carousel/dist/carousel.css'
import { showLoadingToast, closeToast } from 'vant'
import 'vant/es/toast/style'

import { useConfigStore } from '@/stores/config'
import PageBack from '@/components/common/PageBack.vue'
import { KEY, QIONGLAI_LATITUDE, QIONGLAI_LONGITUDE, SAFE_KEY } from '@/common/config'

import icon1Img from '@/assets/images/icon/scenic-spot.png'

const route = useRoute()
const router = useRouter()

/** 对应配置信息，手绘地图及坐标点信息 */
const configStore = useConfigStore()

/** 地图对象 */
let map: any = null

let AMap: any = null

const data = reactive<Record<string, any>>({
  lineDetail: '',
  communityName: '',
  scenicDetail: ''
})

onMounted(() => {
  data.communityName = route.query.communityName
  init()

  // initWxConfig()
})

/** 未开放80或443端口，无法初始化配置 */
// function initWxConfig() {
//   getSign({
//     url: window.location.href
//   }).then((result) => {
//     wx.config({
//       debug: true,
//       appId: result.appId,
//       timestamp: result.timestamp,
//       nonceStr: result.nonceStr,
//       signature: result.signature,
//       jsApiList: ['openLocation', 'getLocation']
//     })
//   })
// }

/** 初始化 */
function init() {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0
  })
  getLineDetail()

  AMapLoader.load({
    key: KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: [
      'AMap.Scale',
      'AMap.HawkEye',
      'AMap.ToolBar',
      'AMap.ControlBar',
      'AMap.Driving',
      'AMap.Transfer',
      'AMap.Walking',
      'AMap.Riding'
    ] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  })
    .then((aMap) => {
      closeToast()
      AMap = aMap

      console.log(AMap)
      map = new AMap.Map('container2', {
        resizeEnable: true,
        //viewMode: '3D',
        // pitch: 30,
        layers: [AMap.createDefaultLayer()],
        center: [
          configStore.config?.longitude || QIONGLAI_LONGITUDE,
          configStore.config?.latitude || QIONGLAI_LATITUDE
        ],
        zoom: 17,
        expandZoomRange: true,
        zooms: [10, 19],
        features: ['bg', 'road', 'building']
      })

      map.addControl(new AMap.Scale())

      const tilerLayer = new AMap.TileLayer({
        zIndex: 10,
        getTileUrl: function (x: string, y: string, z: string) {
          const name = '{x}_{y}'.replace('{x}', x).replace('{y}', y).replace('{z}', z)
          return configStore.config?.path + z + '/' + name + '.png'
        }
      })

      tilerLayer.setMap(map)
      map.on('click', (e: any) => {
        // e.lnglat 获取点击的经纬度
        console.log('点击了地图：', e.lnglat)
        showLineBottom.value = true
      })
    })
    .catch((e) => {
      console.log(e)
    })
}

/** 获取景点详情 */
function getScenicDetail(attractionId: string) {
  getAttractionsDetails({
    attractionId: attractionId
  }).then((result) => {
    data.scenicDetail = result

    map.setCenter([data.scenicDetail.longitude, data.scenicDetail.latitude])
  })
}

/** 获取游线详情 */
function getLineDetail() {
  getDetails({
    tourLineId: route.query.tourLineId
  }).then((result) => {
    data.lineDetail = result

    // 线路大于1的时候才有起点和终点
    if (result.attractionsList.length < 1) {
      console.log('无法构造路线')
      return
    }

    const line = result.attractionsList

    const startPoint = new AMap.LngLat(line[0].longitude, line[0].latitude)
    const endPoint = new AMap.LngLat(
      line[line.length - 1].longitude,
      line[line.length - 1].latitude
    )

    const points = []
    // 取除开始和结束的点，构造途径点
    for (let index = 1; index < line.length - 1; index++) {
      points.push(new AMap.LngLat(line[index].longitude, line[index].latitude))
    }

    const driving = new AMap.Driving({
      policy: AMap.DrivingPolicy.LEAST_TIME
    })

    // 根据起终点经纬度规划驾车导航路线
    driving.search(
      startPoint,
      endPoint,
      {
        waypoints: points
      },
      function (status: string, result: any) {
        // result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
        if (status === 'complete') {
          console.log('绘制驾车路线完成')

          // 绘制第一条路线，也可以按需求绘制其它几条路线
          drawRoute(result.routes[0], line)

          // map.setZoom(18)
          // map.setCenter([line[0].longitude, line[0].latitude])
        } else {
          console.log('获取驾车数据失败：' + result)
        }
      }
    )
  })
}

function drawRoute(route: Record<string, any>, line: Record<string, any>[]) {
  var path = parseRouteToPath(route)

  line.forEach((item) => {
    const marker = new AMap.Marker({
      position: new AMap.LngLat(item.longitude, item.latitude),
      map,
      icon: icon1Img,
      anchor: 'bottom-center',
      offset: new AMap.Pixel(0, 0)
    })

    const markerContent = `<div class="marker-card">${item.attractionName}</div>`

    // 设置label标签
    // label默认蓝框白底左上角显示，样式className为：amap-marker-label
    marker.setLabel({
      direction: 'top',
      offset: new AMap.Pixel(0, 0), //设置文本标注偏移量
      content: markerContent //设置文本标注内容
    })
  })

  const routeLine = new AMap.Polyline({
    path: path,
    isOutline: true,
    outlineColor: '#ffeeee',
    borderWeight: 2,
    strokeWeight: 6,
    strokeOpacity: 0.9,
    strokeColor: '#006633',
    lineJoin: 'round',
    showDir: true
  })

  map.add(routeLine)

  // 调整视野达到最佳显示区域
  map.setFitView([routeLine])
}

// 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
// DrivingResult对象结构参考文档 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DriveRoute
function parseRouteToPath(route: any) {
  let path = []

  for (let i = 0, l = route.steps.length; i < l; i++) {
    var step = route.steps[i]

    for (let j = 0, n = step.path.length; j < n; j++) {
      path.push(step.path[j])
    }
  }

  return path
}

/** 游线弹窗显示状态 */
const showLineBottom = ref(true)

const currentIndex = ref(0)
/** 游线景点详情弹窗显示状态 */
const showDetailBottom = ref(false)

/** 切换线路中的某个地点 */
function change(attractionId: string, index: number) {
  currentIndex.value = index

  showDetailBottom.value = true

  getScenicDetail(attractionId)
}

/** 返回上一页 */
function onClickLeft() {
  router.back()
}

/** 地图导航 */
function toMap() {
  const { longitude, latitude, attractionName, attractionAddress } = data.scenicDetail

  // 跳转微信小程序进行导航
  wx.miniProgram.navigateTo({
    url: `/pages/map/navigation/navigation?longitude=${longitude}&latitude=${latitude}&name=${attractionName}&address=${attractionAddress}`
  })
}
</script>

<template>
  <div class="base-box">
    <!-- 返回 -->
    <PageBack @click="onClickLeft" v-if="!route.query.toDetails"></PageBack>

    <!-- 游线地图 -->
    <div id="container2"></div>

    <!-- 游线弹窗样式1 -->
    <!-- <van-popup v-model:show="showLineBottom" position="bottom" round :overlay="false">
      <PopupTitle :title="data.lineDetail.tourLineName"></PopupTitle>
      <div class="detail">
        <div class="desc">
          <span>总行程 {{ data.lineDetail?.travelDays }}天</span>
          <span>推荐地 {{ data.lineDetail.attractionsList?.length }}处</span>
          <span>{{ data.communityName }}</span>
        </div>
        <div class="content">
          {{ data.lineDetail.tourLineIntroduction }}
        </div>
        <div class="img-box">
          <Carousel v-model="currentIndex" :itemsToShow="3" :wrapAround="true" :transition="500">
            <Slide
              @click="change(item.attractionId, index)"
              v-for="(item, index) in data.lineDetail.attractionsList"
              :key="index"
            >
              <van-image width="100%" height="80" radius="4" :src="item.attractionImages" />
              <div class="item">{{ item.attractionName }}</div>
            </Slide>
          </Carousel>
        </div>
      </div>
    </van-popup> -->

    <!-- 游线弹窗样式2 -->
    <van-popup v-model:show="showLineBottom" position="bottom" round :overlay="false">
      <PopupTitle :title="data.lineDetail.tourLineName"></PopupTitle>
      <div class="detail">
        <div class="desc">
          <span>总行程 {{ data.lineDetail?.travelDays }}天</span>
          <span>推荐地 {{ data.lineDetail.attractionsList?.length }}处</span>
          <span>{{ data.communityName }}</span>
        </div>
        <div class="line-content">
          <div class="line-content-left">
            <div
              class="intro"
              @click="showDetailBottom = false"
              :class="showDetailBottom ? '' : 'intro-active'"
            >
              简介
            </div>
            <div class="left-item">
              <div
                @click="change(item.attractionId, index)"
                v-for="(item, index) in data.lineDetail.attractionsList"
                :key="index"
              >
                <van-image width="100%" height="80" radius="4" :src="item.attractionImages" />
                <div class="item">{{ item.attractionName }}</div>
              </div>
            </div>
          </div>
          <div class="line-content-right">
            <div v-if="showDetailBottom">
              <div class="content-text" v-html="data.scenicDetail.attractionIntroduction"></div>
              <div class="footer">
                <div class="b-box">
                  <div class="address">
                    <van-icon name="location" color="#333333" size="16" />
                    <span>{{ data.scenicDetail.attractionAddress }}</span>
                  </div>
                  <div class="nav" @click="toMap()">
                    <van-icon name="guide-o" size="18" />
                    导航
                  </div>
                </div>
                <div class="b-box">
                  <div>
                    <van-icon name="phone" color="#333333" size="16" />
                    <span>{{ data.scenicDetail.contactPhone }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              {{ data.lineDetail.tourLineIntroduction }}
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 游线景点详情弹窗 -->
    <!-- <van-popup v-model:show="showDetailBottom" position="bottom" round :overlay="false" closeable>
      <div class="base-box">
        <PopupTitle :title="data.scenicDetail.attractionName"></PopupTitle>

        <div class="content-text" v-html="data.scenicDetail.attractionIntroduction"></div>
        <div style="height: 80px"></div>
        <div class="footer">
          <div class="b-box">
            <div class="address">
              <van-icon name="location" color="#333333" size="16" />
              <span>{{ data.scenicDetail.attractionAddress }}</span>
            </div>
            <div id="navigation" class="nav" @click="toMap()">
              <van-icon name="guide-o" size="18" />
              导航
            </div>
          </div>
          <div class="b-box">
            <div>
              <van-icon name="phone" color="#333333" size="16" />
              <span>{{ data.scenicDetail.contactPhone }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup> -->
  </div>
</template>

<style lang="scss" scoped>
#container2 {
  width: 100%;
  height: 80vh;
  position: fixed;
  top: 0;
  left: 0;
}

.amap-logo,
.amap-copyright {
  display: none !important;
}

.detail {
  background: #fff;
  padding: 12px 0;

  .line-content {
    display: flex;
    line-height: 28px;
    margin: 10px 12px;
    font-size: 14px;
    color: #333;
    // overflow-y: auto;
    // max-height: 280px;

    .line-content-left {
      flex-shrink: 0;

      .intro {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 5px 0;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4;
        box-sizing: border-box;
        background: #f6f6f6;
        transition: 0.1s;
      }

      .intro-active {
        color: #2bbc4a;
        border: 1px solid #2bbc4a;
        background: #fafffb;
      }

      .left-item {
        height: 260px;
        overflow-y: auto;
        > div {
          width: 100px;
          position: relative;
          text-align: center;

          .item {
            width: 90px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .line-content-right {
      margin-left: 12px;
      width: 100%;
      > div {
        position: relative;
        height: 280px;
      }
    }
  }
}

.detail .img-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px;
  margin-top: 10px;
}

.detail .img-box .item {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.detail .desc {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #999;
  padding: 0 12px;
}

.detail .content {
  line-height: 28px;
  margin: 10px 12px;
  font-size: 14px;
  color: #333;
  overflow-y: auto;
  max-height: 158px;
}

.carousel {
  width: 100%;
}

.carousel__slide {
  padding: 0 6px;
  height: 90px;
}

.carousel__slide--sliding {
  transition: 0.5s;
}

.carousel__slide--prev {
  opacity: 1;
  transform: rotateY(-10deg) scale(0.95);
}

.carousel__slide--next {
  opacity: 1;
  transform: rotateY(10deg) scale(0.95);
}

.carousel__slide--active {
  opacity: 1;
  transform: rotateY(0) scale(1.1);
}

.img-box .item {
  width: 90px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.base-box {
  position: relative;
}

.map-box {
  position: relative;
  height: 100%;
}

.map-back-mask {
  border-radius: 50%;
  width: 30px;
  height: 30px;
  left: 10px;
  top: 8px;
  position: fixed;
  background: #fff;
}

.map-page {
  left: 10px;
  top: 8px;
}

.content-text {
  // padding: 12px;
  font-size: 15px;
  line-height: 28px;
  max-height: 196px;
  overflow-y: auto;
  // height: 182px;
  // overflow-y: auto;
}

:deep(.content-text img) {
  width: 100% !important;
}

.footer {
  position: absolute;
  bottom: 0;
  background: #fff;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.b-box {
  display: flex;
  align-items: center;
  font-size: 13px;
  justify-content: space-between;
  color: #333;
  line-height: 22px;
  padding-top: 5px;
}

.b-box span {
  margin-left: 5px;
}

.b-box .nav {
  background: #2bbc4a;
  color: #fff;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
  border-radius: 26px;
  font-size: 13px;
  flex-shrink: 0;
}

.b-box .address {
  margin-right: 12px;
}

:deep(.marker-card) {
  font-weight: 400;
  font-size: 13px;
  color: #fff;
  border-radius: 2px;
  padding: 2px 6px;
  border-radius: 2px;
  white-space: nowrap;
  background: #006633;
  border: 1px solid #fff;
}

:deep(.amap-icon img) {
  width: 22px;
}

:deep(.amap-marker-label) {
  border: 0;
  background-color: transparent;
}
</style>
