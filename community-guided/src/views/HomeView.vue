<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref } from 'vue'

import AMapLoader from '@amap/amap-jsapi-loader'
import {
  getDetail,
  getList,
  getListByType,
  getPage,
  getDistrictByHeadChar,
  getCommunityTourConfig,
  addCommunityGuideUsage,
  addLifeCircleUsage,
  addCommunityTourLineUsage,
  getAllDistrictStreet
} from '@/common/api'
import { showLoadingToast, closeToast, showToast } from 'vant'
import 'vant/es/toast/style'

import wx from 'weixin-js-sdk'

import icon1Img from '@/assets/images/icon/poi-marker-default.png'
import icon2Img from '@/assets/images/icon/home.png'
import icon3Img from '@/assets/images/icon/location.png'
import icon4Img from '@/assets/images/icon/change.png'
import icon5Img from '@/assets/images/icon/two.png'
import icon6Img from '@/assets/images/icon/browse.png'

import icon7Img from '@/assets/images/icon/compass.png'
import icon8Img from '@/assets/images/icon/search.png'
import icon9Img from '@/assets/images/icon/position.png'
import icon12Img from '@/assets/images/icon/location-w.png'
import icon13Img from '@/assets/images/icon/eye.png'
import icon14Img from '@/assets/images/icon/eye-s.png'

import tabbarImg1 from '@/assets/images/tabbar/15.png'
import tabbarImg2 from '@/assets/images/tabbar/line.png'

import { useRoute, useRouter } from 'vue-router'

import { useConfigStore } from '@/stores/config'
import SearchList from '@/components/SearchList.vue'
import AreaList from '@/components/AreaList.vue'
import PopupTitle from '@/components/common/PopupTitle.vue'
import PageBack from '@/components/common/PageBack.vue'
import CategoryList from '@/components/index/CategoryList.vue'

import { KEY, QIONGLAI_LATITUDE, QIONGLAI_LONGITUDE, SAFE_KEY } from '@/common/config'
import { PLATFORM_TYPE } from '@/common/enum'

/*********************************************-------------VUE3------------******************************************* */

/** 路由 */
const route = useRoute()
const router = useRouter()

/** 对应配置信息，手绘地图及坐标点信息 */
const configStore = useConfigStore()

/*********************************************-------------手绘地图------------******************************************* */

/** 显示/隐藏手绘地图 */
const showChartlet = ref(true)
/** 当前定位状态 */
const locationState = ref(true)
/** 显示所有标记 */
const showAllMarker = ref(true)
/** 贴图层 */
const tilerLayer = ref()
/** 指南针角度 */
const rotate = ref(0)

/** 当前区域名称 */
const currentAreaName = ref('')

/*********************************************-------------底部 tabbar------------******************************************* */
/** 生活圈弹窗状态（打开/关闭) */
const showPopover = ref(false)
/** 游线弹窗状态（打开/关闭) */
const showBottomLine = ref(false)

/** 控制底部按钮显示 - 根据URL参数showLine决定 */
const showLifeCircleBtn = ref(true)
const showTourLineBtn = ref(true)

/** 生活圈列表 */
const typeList = ref()

/** 游线分页参数 */
const pageSize = ref(10)
const pageNum = ref(1)
/** 游线列表 */
const lineList = ref<Record<string, any>[]>([])
/** 当前游线列表类型（最新，最热） */
const currentSortType = ref(1)
/** 游线列表加载中状态*/
const loading = ref(false)
/** 游线列表是否全部加载完成状态 */
const finished = ref(false)
/** 显示全部游线列表 */
const showAllLine = ref(false)

/*********************************************-------------其他------------******************************************* */

/** 区域编码 */
/** 最近选择的区域列表 */
const districtCode = ref()
const history = ref<Record<string, any>[]>([])
/** 高德地图 */
let map: any = null
let AMap: any = null

/** 区域列表侧边栏弹出层状态（打开/关闭) */
const showIndexListSidebar = ref(false)
/** 默认公共服务的id */
const defaultTypeId = ref('')

const data = reactive<Record<string, any>>({
  /** 社区 */
  community: '',
  /** 游线详情 */
  lineDetail: ''
})

onMounted(async () => {
  // 获取参数
  districtCode.value = route.query.code
  currentAreaName.value = route.query.name as string
  const showLine = route.query.showLine

  // 根据showLine参数控制按钮显示
  if (showLine === 'true') {
    // 只显示社区游线按钮
    showLifeCircleBtn.value = false
    showTourLineBtn.value = true
  } else if (showLine === 'false') {
    // 只显示15分钟生活圈按钮
    showLifeCircleBtn.value = true
    showTourLineBtn.value = false
  } else {
    // 默认情况：显示两个按钮
    showLifeCircleBtn.value = true
    showTourLineBtn.value = true
  }

  // 初始化标记点事件
  initMarkerClick()

  addCommunityGuideCount()

  getHistoryArea()

  compass()

  const data = await getCommunityTourConfig({ districtCode: districtCode.value })

  configStore.setConfig(data)

  init()
  getTypeList()

  // 判断页面打开时是否需要展示游线
  // if (showLine === 'true') {
  //   // 展示游线
  //   change(1)
  // }

  navigator.geolocation.getCurrentPosition(
    function (pos) {
      locationState.value = false
    },
    function (err) {
      locationState.value = true
    }
  )
})

/** 组件卸载时清理语音播报 */
onUnmounted(() => {
  stopVoicePlay()
})

/** 获取最近选择过的区域 */
function getHistoryArea() {
  const historyList = route.query.history as string
  if (historyList) {
    history.value = JSON.parse(historyList)
  }
}

/** 添加记录次数 */
function addCommunityGuideCount() {
  addCommunityGuideUsage({
    districtCode: districtCode.value
  }).then((res) => {
    console.log('plus one')
  })
}

/** 初始化 */
function init() {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0
  })

  getCommunityDetail()

  window._AMapSecurityConfig = {
    securityJsCode: SAFE_KEY // 密钥
  }

  AMapLoader.load({
    key: KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ['AMap.Scale', 'AMap.HawkEye', 'AMap.ToolBar', 'AMap.ControlBar', 'AMap.ElasticMarker'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  })
    .then((aMap) => {
      closeToast()
      AMap = aMap
      map = new AMap.Map('container', {
        resizeEnable: true,
        //viewMode: '3D',
        // pitch: 30,
        layers: [AMap.createDefaultLayer()],
        center: [
          configStore.config?.longitude || QIONGLAI_LONGITUDE,
          configStore.config?.latitude || QIONGLAI_LATITUDE
        ],
        zoom: 17,
        expandZoomRange: true,
        zooms: [16, 19],
        features: ['bg', 'road', 'building']
      })

      map.addControl(new AMap.Scale())

      tilerLayer.value = new AMap.TileLayer({
        zIndex: 10,
        getTileUrl: function (x: string, y: string, z: string) {
          const name = '{x}_{y}'.replace('{x}', x).replace('{y}', y).replace('{z}', z)
          return configStore.config?.path + z + '/' + name + '.png'
        }
      })

      tilerLayer.value.setMap(map)

      // 获取标记点，默认显示公共服务
      getMarkerList({ typeId: defaultTypeId.value })

      // map.on('click', (e) => {
      //   // e.lnglat 获取点击的经纬度
      //   console.log('点击了地图：', e.lnglat)
      //   showBottom.value = true
      // })
    })
    .catch((e) => {
      console.log(e)
    })
}

/** 指南针 */
function compass() {
  var ua = navigator.userAgent.toLowerCase()

  if (/android/.test(ua)) {
    window.addEventListener(
      'deviceorientationabsolute',
      function (e: any) {
        rotate.value = Math.round(315 - e.alpha!)
      },
      false
    )
  } else {
    window.addEventListener(
      'deviceorientation',
      function (e: any) {
        rotate.value = Math.round(315 - e.webkitCompassHeading)
      },
      false
    )
  }
}

/** 获取社区详情 */
function getCommunityDetail() {
  getDetail({
    districtCode: districtCode.value
  }).then((result) => {
    data.community = result

    if (data.community) {
      // 当前显示区域
      currentAreaName.value = data.community.communityName
    }
  })
}

/** tabbar 15分钟生活圈为0标识，社区游线为1标识 */
async function change(e: number) {
  if (e == 0) {
    addLifeCircleUsage({
      districtCode: districtCode.value,
      platformType: PLATFORM_TYPE
    }).then((res) => {
      console.log('plus one. Tab1')
    })

    if (!showPopover.value) {
      showBottomLine.value = false
      showPopover.value = true
    } else {
      showPopover.value = false
    }
  } else {
    addCommunityTourLineUsage({
      districtCode: districtCode.value,
      platformType: PLATFORM_TYPE
    }).then((res) => {
      console.log('plus one. Tab2')
    })

    showPopover.value = false
    showBottomLine.value = true

    lineList.value = []
    await getLinePage()

    // 未查询到游线数据则尝试查询全部
    if (lineList.value.length <= 0) {
      changeShowAllLine(true)
    }
  }
}

/** 获取生活圈类型列表 */
function getTypeList() {
  getList().then((result) => {
    typeList.value = result
  })
}

/** 生活圈标记点列表 */
const markers = ref<Record<string, any>[]>([])

/**
 * 根据区域及类型id获取对应标记点,并将其标记在地图上
 * @param item 区域编码及类型id(文化服务/公共服务等)
 */
function getMarkerList(item: Record<string, any>) {
  getListByType({
    districtCode: districtCode.value,
    typeId: item.typeId
  }).then((result) => {
    if (markers.value.length > 0) {
      map.remove(markers.value)
    }

    console.log(wx)

    result.forEach((el: Record<string, any>) => {
      // 自定义的标记点内容样式
      const markerContent = `<div class="custom-content-marker">
                                  <div class="marker-content marker-hide">
                                      <img src="${el.circleImages}" alt="">
                                      <div class="marker-text">
                                        <div>
                                          <span>名称:</span>
                                          <p> ${el.circleName}</p>
                                        </div>
                                        <div>
                                          <span>电话:</span>
                                          <p class="marker-phone" style="${el.contactPhone ? 'text-decoration: underline;' : ''}"> ${el.contactPhone || '无'}</p>
                                        </div>
                                        <div>
                                          <span>地址:</span>
                                          <p class="marker-address"> ${el.circleAddress}</p>
                                        </div>
                                      </div>
                                  </div>
                                  <div onclick="onMarkerClick(this)" class="marker-buttom-box">
                                    <div class="marker-buttom-content" style="background: #FF9F18">
                                      ${el.circleName}
                                    </div>
                                    <img src="${el.mapIcon}" alt="">
                                  </div>
                              </div>`

      const marker = new AMap.Marker({
        position: new AMap.LngLat(el.longitude, el.latitude),
        offset: new AMap.Pixel(-10, -10),
        content: markerContent,
        // icon: startIcon,
        title: el.circleName
      })
      map.add(marker)
      markers.value.push(marker)

      // 监听标记点击事件, 当某个标记点被点击时, 则将其设置为顶层显示
      marker.on('click', function (e: any) {
        // 当前dom
        const currentMarkerDom = e.target.dom

        /** 一、地址点击-导航处理 */

        // 解析经纬度
        const { lng, lat } = e.lnglat
        // 标记点名称
        const attractionName = currentMarkerDom.querySelector('.marker-buttom-content').innerText
        // 地址dom节点
        const addressDom = currentMarkerDom.querySelector('.marker-address')
        // 详细地址信息
        const attractionAddress = addressDom.innerText
        // 点击地址添加点击事件跳转小程序地图导航
        addressDom.onclick = function () {
          // 跳转微信小程序进行导航
          wx.miniProgram.navigateTo({
            url: `/pages/map/navigation/navigation?longitude=${lng}&latitude=${lat}&name=${attractionName}&address=${attractionAddress}`
          })
        }

        /** 二、电话点击-拨号处理 */

        // 电话dom节点
        const phoneDom = currentMarkerDom.querySelector('.marker-phone')
        // 电话号码
        const phoneNumber = phoneDom.innerText
        // 有值则绑定点击事件，无值则不处理
        if (phoneNumber) {
          phoneDom.onclick = function () {
            // 跳转小程序内打开拨号界面
            wx.miniProgram.navigateTo({
              url: `/pages/map/dialing/dialing?phoneNumber=${phoneNumber}`
            })
          }
        }

        // 点击标记点显示在最顶部
        marker.setTop(true)
      })
    })
    closeToast()
  })
}

// 地图自定义标记点击时
function initMarkerClick() {
  window.onMarkerClick = function (_this: HTMLDivElement) {
    // 上一个
    const sibling = _this.previousElementSibling
    // 下一个
    const nextSibling = _this.nextElementSibling
    const isActive = sibling?.classList.contains('marker-content-active')

    // 如果已经选中，则去掉样式
    if (isActive) {
      sibling?.classList.add('marker-hide')
      sibling?.classList.remove('marker-content-active')
      _this.classList.remove('marker-active')
      return
    }

    // 选中其他时，先去掉所有样式
    const marker = document.querySelectorAll('.marker-content')
    marker.forEach((el) => {
      el.classList.remove('marker-content-active')
      el.classList.add('marker-hide')

      el.nextElementSibling?.classList.remove('marker-active')
    })

    // 添加点击的样式
    sibling?.classList.add('marker-content-active')
    sibling?.classList.remove('marker-hide')
    _this.classList.add('marker-active')
  }
}

/** 分页获取游线列表 */
async function getLinePage(code?: string) {
  const result = await getPage({
    pageSize: pageSize.value,
    pageNum: pageNum.value,
    districtCode: code == '' ? code : districtCode.value,
    sortType: currentSortType.value
  })

  if (result.records) {
    lineList.value = lineList.value.concat(result.records)
    loading.value = false
    if (result.records.length < pageSize.value) {
      finished.value = true
    } else {
      pageNum.value += 1
    }
  }
}

/** 根据最新和最热获取列表 */
function changeSortType(type: number) {
  currentSortType.value = type
  lineList.value = []
  pageNum.value = 1
  getLinePage()
}

/**
 * 根据对应游线id获取游线详情
 * @param item 游线详情对应
 */
function toDetail(item: Record<string, any>) {
  data.lineDetail = item

  router.push({
    path: 'lineMap',
    query: {
      tourLineId: item.tourLineId,
      communityName: data.community?.communityName
    }
  })

  // wx.miniProgram.navigateTo({
  //   url: `/pages/map/line/line?tourLineId=${item.tourLineId}&communityName=${data.community?.communityName}`
  // })
}

/** 从区域列表侧边栏弹出层返回主页（即隐藏弹出层） */
function onClickLeft() {
  showIndexListSidebar.value = false
}

/** 切换区域 */
function changePosition() {
  // 判断运行环境
  const isMiniProgram = /miniProgram/i.test(navigator.userAgent.toLowerCase())

  if (isMiniProgram) {
    // 小程序环境
    wx.miniProgram.navigateTo({
      url: `/pages/index/area/area?scene=map`
    })
  } else {
    // 兼容h5
    showIndexListSidebar.value = true
    getIndexList()
  }

  // wx.miniProgram.getEnv(function (res) {
  //   console.log(res.miniprogram)
  // })
}

/** 获取区域列表 */
function getIndexList() {
  getDistrictByHeadChar({
    name: ''
  }).then((result) => {
    console.log(result)
    const { district, hotDistrict, headChars } = result
    data.district = district
    data.hotDistrict = hotDistrict
    data.headChars = headChars
  })
}

/** 选择对应区域 */
async function selectArea(data: Record<string, any>) {
  districtCode.value = data.code
  currentAreaName.value = data.name
  getCommunityDetail()
  showIndexListSidebar.value = false

  // 获取区域所有标记点
  getMarkerList({ typeId: defaultTypeId.value })

  lineList.value = []
  pageNum.value = 1
  showAllLine.value = false
  await getLinePage()

  // 未查询到游线数据则尝试查询全部
  if (lineList.value.length <= 0) {
    changeShowAllLine(true)
  }
}

/** 显示搜索界面 */
const showSearchPage = ref(false)

/** 手绘地图开关 */
function switchBaseMap() {
  showChartlet.value = !showChartlet.value

  if (showChartlet.value) {
    // 将手绘图层添加到地图上
    tilerLayer.value.setMap(map)
  } else {
    // 删除手绘图层
    map.remove(tilerLayer.value)
  }
}

/** 显示搜索生活圈面板 */
function showSearchPanel() {
  showSearchPage.value = true
}

/** 显示或隐藏所有标记点 */
function showAllMarkerPoint() {
  showAllMarker.value = !showAllMarker.value
  if (showAllMarker.value) {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      loadingType: 'spinner',
      duration: 0
    })
    getMarkerList({ typeId: '' })
  } else {
    map.remove(markers.value)
  }
}

/** 定位到标记点 */
function toMarkerPoint(item: Record<string, any>) {
  getMarkerList({
    typeId: item.typeId
  })
  map.setCenter([item.longitude, item.latitude])
}

/** 当前位置 */
const currentLocation = ref(false)

/** 获取当前位置 */
function getLocation() {
  showLoadingToast({
    message: '正在定位...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0
  })
  navigator.geolocation.getCurrentPosition(success, error)
}

/** 当前所在位置标记点 */
const currentMarker = ref()

/** 获取自身定位，并设置对应的标记点（定位成功时触发） */
function success(pos: any) {
  currentLocation.value = true

  const crd = pos.coords

  // 存在标记点就不要创建了，只需要把当前的经纬度重新设置即可
  if (currentMarker.value) {
    currentMarker.value.setPosition(new AMap.LngLat(crd.longitude, crd.latitude))
  } else {
    const icon = new AMap.Icon({
      size: new AMap.Size(20, 26), //图标尺寸
      image: icon1Img, //Icon 的图像
      imageSize: new AMap.Size(20, 26) //根据所设置的大小拉伸或压缩图片
    })

    currentMarker.value = new AMap.Marker({
      position: new AMap.LngLat(crd.longitude, crd.latitude),
      offset: new AMap.Pixel(-10, -10),
      icon: icon
    })

    map.add(currentMarker.value)
  }

  // 设置地图中心点
  map.setCenter([crd.longitude, crd.latitude])

  console.log('Your current position is:')
  console.log('Latitude : ' + crd.latitude)
  console.log('Longitude: ' + crd.longitude)
  console.log('More or less ' + crd.accuracy + ' meters.')

  closeToast()
}

/** 定位失败触发 */
function error(err: any) {
  console.warn('ERROR(' + err.code + '): ' + err.message)
  showToast('定位失败')
  closeToast()
}

/** 返回社区 */
function backCommunity() {
  currentLocation.value = false
  map.setCenter([configStore.config?.longitude, configStore.config?.latitude])
}

/** 是否显示社区弹框 */
const showCommunityDetail = ref(true)

/** 语音播报相关 */
const isPlaying = ref(false)
let currentAudioElement: HTMLAudioElement | null = null

/** 切换语音播报 */
function toggleVoicePlay() {
  // 优先检查是否有voiceUrl
  if (data.community?.voiceUrl) {
    if (isPlaying.value) {
      stopVoicePlay()
    } else {
      playVoiceFromUrl()
    }
    return
  }

  // 如果没有voiceUrl，直接提示暂无语音播报
  showToast('暂无语音播报')
  return
}

/** 播放语音URL */
function playVoiceFromUrl() {
  try {
    const voiceUrl = data.community?.voiceUrl
    if (!voiceUrl) {
      showToast('语音地址无效')
      return
    }

    console.log('开始播放语音URL:', voiceUrl)
    showLoadingToast({
      message: '正在加载语音...',
      forbidClick: true,
      loadingType: 'spinner',
      duration: 0
    })

    // 创建音频元素
    currentAudioElement = new Audio(voiceUrl)
    currentAudioElement.volume = 1.0

    // 监听音频加载完成
    currentAudioElement.oncanplaythrough = () => {
      closeToast()
      console.log('语音加载完成，开始播放')
      isPlaying.value = true
    }

    // 监听播放开始
    currentAudioElement.onplay = () => {
      console.log('语音开始播放')
      isPlaying.value = true
    }

    // 监听播放结束
    currentAudioElement.onended = () => {
      console.log('语音播放完成')
      isPlaying.value = false
      currentAudioElement = null
    }

    // 监听播放错误
    currentAudioElement.onerror = (error) => {
      console.error('语音播放失败:', error)
      closeToast()
      isPlaying.value = false
      currentAudioElement = null
      showToast('语音播放失败')
    }

    // 监听加载错误
    currentAudioElement.onloadstart = () => {
      console.log('开始加载语音文件')
    }

    // 开始播放
    currentAudioElement.play().catch(error => {
      console.error('播放失败:', error)
      closeToast()
      isPlaying.value = false
      currentAudioElement = null
      showToast('语音播放失败')
    })

  } catch (error) {
    console.error('播放语音URL异常:', error)
    closeToast()
    isPlaying.value = false
    showToast('语音播放异常')
  }
}



/** 停止语音播报 */
function stopVoicePlay() {
  try {
    // 停止音频播放
    if (currentAudioElement) {
      currentAudioElement.pause()
      currentAudioElement.currentTime = 0
      currentAudioElement = null
    }

    isPlaying.value = false
  } catch (error) {
    console.error('停止语音播报失败:', error)
  }
}

/** 显示全部线路 */
function changeShowAllLine(state: boolean) {
  showAllLine.value = state

  lineList.value = []
  pageNum.value = 1

  if (state) {
    getLinePage('')
  } else {
    getLinePage()
  }
}




</script>

<template>
  <div>
    <div class="top-left-box">
      <!-- 区域 -->
      <div class="location-box" @click="changePosition()">
        <div>
          <van-image :src="icon3Img" width="13" height="16" />
          <span>{{ currentAreaName }}</span>
        </div>
        <div @click="changePosition()">
          <van-image :src="icon4Img" width="15" height="14" />
        </div>
      </div>
      <!-- 社区详情弹窗 -->
      <div class="home-box">
        <van-image
          @click="showCommunityDetail = !showCommunityDetail"
          :src="icon2Img"
          width="17"
          height="18"
        />
        <div
          class="community-detail"
          :class="data.community && showCommunityDetail ? 'community-detail-show' : ''"
        >
          <div class="info">
            <van-image :src="data.community?.communityImages" width="100" height="100" round />
            <div class="right-box">
              <span>{{ data.community?.communityName }}</span>
              <p><span>地址：</span> {{ data.community?.communityAddress }}</p>
              <p><span>电话：</span> {{ data.community?.communityPhone }}</p>
              <div class="voice-btn" :class="{ 'playing': isPlaying }" @click="toggleVoicePlay">
                <span v-if="!isPlaying">语音播报</span>
                <span v-else>停止播报</span>
              </div>
            </div>
          </div>
          <div class="intro">
            <div>
              <van-image :src="tabbarImg1" width="20" height="20" round />
              <span>社区介绍</span>
            </div>
            <p>
              {{ data.community?.communityIntroduction }}
            </p>
          </div>
          <div class="hide" @click="showCommunityDetail = false">
            <span>收起</span>
            <van-icon name="arrow-up" color="#999999" size="12" />
          </div>
        </div>
      </div>
    </div>
    
    <van-popup
      v-model:show="showIndexListSidebar"
      position="right"
      style="width: 100%; height: 100%"
    >
      <PageBack @click="onClickLeft"></PageBack>
      <AreaList @selectArea="selectArea" />
    </van-popup>

    <van-popup
      v-model:show="showBottomLine"
      position="bottom"
      style="height: 500px"
      round
      :overlay="false"
      closeable
    >
      <div class="line">
        <div>
          <PopupTitle :title="showAllLine ? '邛崃游线' : '社区游线'"></PopupTitle>

          <div class="switch-box">
            <div class="left-box">
              <div :class="showAllLine ? '' : 'active'" @click="changeShowAllLine(false)">
                <van-image v-if="showAllLine" :src="icon3Img" width="13" height="15" />
                <van-image v-else :src="icon12Img" width="13" height="15" />
                <span>{{ data.community?.communityName }}</span>
              </div>
              <div :class="showAllLine ? 'active' : ''" @click="changeShowAllLine(true)">全部</div>
            </div>
            <div class="right-box">
              <div
                class="new"
                :class="currentSortType == 1 ? 'active' : ''"
                @click="changeSortType(1)"
              >
                最新
              </div>
              <div
                class="hot"
                :class="currentSortType == 2 ? 'active' : ''"
                @click="changeSortType(2)"
              >
                最热
              </div>
            </div>
          </div>
          <div class="list">
            <van-empty v-if="lineList.length < 1" image="search" description="未查询到景点线路" />
            <van-list
              v-else
              v-model:loading="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="getLinePage"
            >
              <div
                class="item"
                v-for="(item, index) in lineList"
                :key="index"
                @click="toDetail(item)"
              >
                <div class="img-list">
                  <van-image
                    v-for="el in 3"
                    :key="el"
                    width="33%"
                    height="80"
                    :src="item.attractionsList[el - 1]?.attractionImages"
                  />
                </div>
                <div class="title">{{ item.tourLineName }}</div>
                <div class="buttom-info">
                  <div>
                    <div>总行程 {{ item.travelDays }}天</div>
                    &nbsp;&nbsp;&nbsp;
                    <div>推荐地 {{ item.attractionsList.length }}处</div>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 地图 -->
    <div id="container"></div>

    <!-- 底图开关 -->
    <div class="base-map">
      <div v-show="showChartlet" @click="switchBaseMap()">
        <van-image :src="icon6Img" width="20" height="22" />
        <span>游览</span>
      </div>
      <div v-show="!showChartlet" @click="switchBaseMap()">
        <van-image :src="icon5Img" width="20" height="20" />
        <span>二维</span>
      </div>
      <!-- <div>
        <van-image :src="icon6Img" width="20" height="20" />
        <span>社区</span>
      </div> -->
    </div>

    <!-- 定位相关 -->
    <div class="locating-group">
      <div @click="showSearchPanel()">
        <van-image :src="icon8Img" width="17" height="17" />
      </div>
      <div @click="showAllMarkerPoint()">
        <van-image v-show="showAllMarker" :src="icon14Img" width="18" height="13" />
        <van-image v-show="!showAllMarker" :src="icon13Img" width="18" height="13" />
      </div>
      <div :style="{ transform: 'rotate(-' + rotate + 'deg)' }">
        <van-image :src="icon7Img" width="15" height="15" />
      </div>
      <div @click="getLocation()">
        <van-image :src="icon9Img" width="18" height="18" />
      </div>
    </div>

    <div class="back-btn-box" v-if="currentLocation">
      <van-button icon="revoke" round color="#2bbc4a" @click="backCommunity()">返回社区</van-button>
    </div>

    <!-- 搜索生活圈 -->
    <!-- <div class="search-box">
      <div @click="showSearchPanel()">
        <van-image :src="icon8Img" width="17" height="17" />
      </div>
    </div> -->

    <SearchList
      v-model="showSearchPage"
      :districtCode="districtCode"
      @toMarkerPoint="toMarkerPoint"
    ></SearchList>

    <van-popup v-model:show="showPopover" position="bottom" round :overlay="false">
      <div class="close" @click="showPopover = false">
        <div>
          <van-icon name="arrow-down" color="#666666" size="10" />
        </div>
      </div>
      <!-- <van-grid square clickable :border="false" column-num="4">
        <van-grid-item v-for="(item, index) in typeList" :key="index" @click="getMarkerList(item)">
          <van-image width="54" height="54" :src="item.typeIcon" />
          <div class="type-name">
            {{ item.typeName }}
          </div>
        </van-grid-item>
      </van-grid> -->

      <CategoryList @click="getMarkerList"></CategoryList>

      <div style="height: 14px"></div>
    </van-popup>

    <footer class="footer-tabbar">
      <!-- v-show="showLifeCircleBtn" -->
      <div @click="change(0)">
        <van-image :src="tabbarImg1" width="29" height="29" />
        <span>15分钟生活圈</span>
      </div>
      <!-- <div v-show="showTourLineBtn" @click="change(1)">
        <van-image :src="tabbarImg2" width="29" height="29" />
        <span>社区游线</span>
      </div> -->
    </footer>
  </div>
</template>

<style lang="scss">
/** 地图自定义标记点样式 */
@import './styles/marker.scss';

#container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
}

.amap-logo,
.amap-copyright {
  display: none !important;
}

.tabbar {
  flex: none;
}

.tabbar-title {
  height: 50px;
  line-height: 50px;
  color: #666;
}

.type-name {
  font-weight: 400;
  font-size: 12px;
  color: #333333;
}

:deep(.van-tabbar-item) {
  font-size: 15px;
}

.detail > div {
  /* margin: 0 10px; */
  /* box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.2); */
  overflow: hidden;
  position: relative;
  /* border-radius: 10px; */
  /* background: #fff; */
}
.voice-btn{
  color: #2bbc4a;
  font-size: 12px;
  height: 30px;
  margin-top: 10px;
  border-radius: 4px;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: flex-start;
  background: url('@/assets/voice.png') no-repeat left center;
  background-size: 15px 15px;
  padding-left: 18px;
  cursor: pointer;
  transition: all 0.3s ease;

  
  &:active {
    transform: scale(0.95);
  }
  
  &.playing {
    color: #ff6b6b;
    animation: pulse 1.5s infinite;
    background: url('@/assets/voice2.png') no-repeat left center;
    background-size: 15px 15px;
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
.detail > div .title {
  color: rgb(66, 70, 86);
  font-size: 15px;
  font-weight: 600;
  padding: 6px 0;
}

.detail > div .base-info {
  padding: 0 12px;
  font-size: 14px;
  line-height: 28px;
  color: #666;
}

.detail > div .close-icon {
  position: absolute;
  right: 15px;
  top: 15px;
}

.detail > div .base-info > div {
  display: flex;
  align-items: center;
}

.detail > div .base-info .content {
  padding-bottom: 12px;
}

.detail > div .base-info > div .img-box {
  /* width: 20px;
  height: 16px; */
  text-align: center;
  margin-right: 5px;
}

.detail > div .base-info > div .img-box img {
  height: 100%;
}

.tabbar-item {
  display: flex;
  align-items: center;
}
.tabbar-item span {
  margin-left: 6px;
}

.line {
  width: 100%;
}

.line > div {
  /* margin: 0 10px; */
  background: #fff;
  border-radius: 10px;
}

.line > div .switch-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
}

.line > div .switch-box .left-box {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  background: #f3f3f3;
  height: 28px;
  border-radius: 14px;
  overflow: hidden;

  > div {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // border-radius: 14px;
    &:first-of-type {
      > span {
        margin-left: 6px;
      }
    }
  }

  .active {
    background: #2bbc4a;
    color: #fff;
  }
}

.line > div .switch-box .right-box {
  display: flex;
  align-items: center;
  height: 28px;
  background: #f3f3f3;
  border-radius: 14px;
  // border: 1px solid rgba(255, 105, 57, 1);
}

.line > div .switch-box .right-box .new {
  width: 46px;
  height: 100%;
  display: flex;
  border-radius: 24px;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.line > div .switch-box .right-box .hot {
  width: 46px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
  font-weight: 500;
}

.line > div .switch-box .right-box .active {
  background: #2bbc4a;
  color: #fff;
}

.line > div .list {
  height: 390px;
  overflow-y: auto;
  padding: 0 12px;
}

.line > div .list .item {
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.1);
  margin-top: 14px;
  border-radius: 10px;
  overflow: hidden;
}

.line > div .list .item .img-list {
  display: flex;
  justify-content: space-between;
}

.line > div .list .item .title {
  font-size: 14px;
  font-weight: 500;
  margin: 5px 10px;
}

.line > div .list .item .buttom-info {
  display: flex;
  justify-content: space-between;
  margin: 0 10px 10px 10px;
}

.line > div .list .item .buttom-info > div {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.top-left-box {
  display: flex;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 200;
  width: 100%;
  justify-content: space-between;
  padding: 9px;
  box-sizing: border-box;
  > div {
    height: 44px;
    background: #ffffff;
    box-shadow: 0px 0px 19px 1px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }

  .location-box {
    width: calc(100% - 53px);
    box-sizing: border-box;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    > div {
      height: 100%;
      display: flex;
      align-items: center;
      > span {
        margin-left: 7px;
      }
    }
  }

  .home-box {
    width: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    position: relative;

    .hide {
      position: absolute;
      right: 18px;
      top: 18px;

      font-weight: 500;
      font-size: 12px;
      color: #999999;

      display: flex;
      align-items: center;
      span {
        margin-right: 4px;
      }
    }

    .community-detail {
      position: fixed;
      top: 32px;
      right: 22px;
      width: calc(100% - 18px);
      transform: scale(0);
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      margin: 0 9px;
      padding: 20px;
      box-sizing: border-box;
      // opacity: 0;
      transition: 0.4s;
      transform-origin: top right;
      z-index: -1;

      .info {
        display: flex;
        .van-image {
          flex-shrink: 0;
        }
        .right-box {
          margin-left: 15px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          > p {
            font-weight: 500;
            font-size: 13px;
            color: #333333;
            margin-top: 10px;
            > span {
              color: #999999;
            }
          }
          > span {
            font-weight: bold;
            font-size: 15px;
            color: #333333;
          }
        }
      }

      .intro {
        margin-top: 20px;
        padding: 16px 15px;
        background: rgba(218, 241, 223, 0.7);
        border-radius: 5px;
        > div {
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          > span {
            margin-left: 7px;
            font-weight: bold;
            font-size: 14px;
            color: #333333;
          }
        }

        > p {
          font-weight: 400;
          font-size: 13px;
          color: #666666;
          line-height: 26px;
        }
      }
    }

    .community-detail-show {
      transform: scale(1);
      top: 68px;
      right: 0;
      // right: 9px
      // opacity: 1;
    }
  }
}

/* .custom-content-marker .marker-buttom-content-active {
  transform: scale(1.3);
} */

.index-title {
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
  line-height: 32px;
  color: #333;
}

.content-box {
  display: flex;
  flex-wrap: wrap;
}

.content-box > div {
  padding: 10px 20px;
  color: #666;
  font-size: 14px;
}

:deep(.van-index-anchor) {
  background-color: #f7f8fa;
}

.van-index-bar {
  height: 100vh !important;
  overflow-y: auto;
}

/** map 控件 */
.locating-group {
  position: fixed;
  left: 9px;
  bottom: 200px;

  > div {
    width: 36px;
    height: 36px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    box-shadow: 0px 0px 10px 0px rgba(194, 194, 194, 0.3);
    margin-top: 12px;
  }
}

.base-map {
  position: fixed;
  right: 9px;
  top: 79px;
  width: 44px;
  height: 50px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(194, 194, 194, 0.3);
  border-radius: 5px;
  box-sizing: border-box;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 6px;
    // border-bottom: 1px solid #dfdfdf;

    &:first-of-type {
    }
    &:last-of-type {
      border-bottom: none;
    }
    > span {
      font-weight: 500;
      font-size: 10px;
      color: #333333;
      margin-top: 3px;
    }
  }
}

.search-box {
  position: fixed;
  right: 9px;
  bottom: 200px;
  > div {
    width: 36px;
    height: 36px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    box-shadow: 0px 0px 10px 0px rgba(194, 194, 194, 0.3);
  }
}

.back-btn-box {
  position: fixed;
  left: 50%;
  bottom: 15%;
  transform: translateX(-50%);
  .van-button {
    height: 34px;
    font-weight: 500;
    font-size: 14px;
    color: #fff !important;
  }
}

.footer-tabbar {
  position: fixed;
  left: 0;
  bottom: 0;
  box-sizing: border-box;
  padding: 9px;
  width: 100%;
  display: flex;
  justify-content: space-between;

  > div {
    height: 49px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 15px;
    width: calc(100% - 6px);
    color: #333333;
    > span {
      margin-left: 5px;
    }

    &:first-of-type {
      background: #daf1df;
    }

    &:last-of-type {
      background: #dcf3f1;
    }
  }
}

.close {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-top: 10px;
  > div {
    width: 22px;
    height: 12px;
    background: #e0e0e0;
    border-radius: 2px;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
