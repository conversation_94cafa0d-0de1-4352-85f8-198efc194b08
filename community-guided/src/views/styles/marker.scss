// 地图标记点样式

.custom-content-marker {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-content-marker .marker-buttom-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: 0.2s;
  transform-origin: bottom center;
}

.custom-content-marker .marker-buttom-box img {
  width: 22px;
  margin-top: 3px;
}

.custom-content-marker .marker-active {
  transform: scale(1.2);
}

.custom-content-marker .marker-content {
  position: absolute;
  bottom: 100%;
  background: rgba(255, 255, 255, 0.9);
  padding: 14px 10px;
  border-radius: 8px;
  transition: 0.3s;
  transform: scale(0);
  width: 272px;
  display: flex;
  align-items: center;
  transform-origin: bottom center;
}

.custom-content-marker .marker-content img {
  height: 72px;
  width: 72px;
  flex-shrink: 0;
  margin-right: 10px;
  border-radius: 10px;
}

.custom-content-marker .marker-content-active {
  transform: scale(1);
  bottom: calc(100% + 20px);
}

.custom-content-marker .marker-content::after {
  content: '';
  bottom: -9px;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid rgba(255, 255, 255, 0.9);
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
}

.custom-content-marker .marker-content .marker-text {
  overflow-y: auto;
  font-size: 13px;
  line-height: 20px;

  > div {
    display: flex;
    // align-items: center;
    span {
      color: #999999;
      font-weight: 400;
      white-space: nowrap;
      margin-right: 6px;
    }
    p {
      font-weight: 500;
      color: #333333;
    }
  }
}

.custom-content-marker .marker-buttom-content {
  font-weight: 400;
  font-size: 13px;
  color: #ffffff;
  border-radius: 2px;
  padding: 2px 6px;
  border-radius: 20px;
  white-space: nowrap;
}

.custom-content-marker .marker-content .marker-text .marker-address {
  text-decoration: underline;
}
