<script setup lang="ts">
import { ref, watch } from 'vue'
import { RouterView, useRoute } from 'vue-router'

const transition = ['slide-left', 'slide-right']
const transitionName = ref(transition[0])

const route = useRoute()

// 监控路由的变化
watch(
  () => route.meta.index as number,
  (newIndex: number, oldIndex: number) => {
    if (newIndex > oldIndex) {
      transitionName.value = transition[0]
    } else {
      transitionName.value = transition[1]
    }
  }
)
</script>

<template>
  <RouterView v-slot="{ Component, route }">
    <transition :name="transitionName">
      <KeepAlive exclude="LineMapView">
        <component :is="Component" :key="route.path" />
      </KeepAlive>
    </transition>
  </RouterView>
  <!-- <RouterView /> -->
</template>

<style>
* {
  margin: 0;
  padding: 0;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.4s;
  background-color: white;
}

.slide-left-enter-from,
.slide-right-leave-to {
  opacity: 1;
  transform: translateX(100%);
}

.slide-right-enter-from,
.slide-left-leave-to {
  opacity: 1;
  transform: translateX(-100%);
}

.slide-left-leave-to,
.slide-right-leave-to {
  opacity: 0.4;
}
</style>
