import { showLoadingToast, closeToast, showToast } from 'vant'
import 'vant/es/toast/style'

/**
 * 请求相关
 *
 * <AUTHOR>
 * @date 2023/1/3 01:14
 * @version 1.0.0
 */
class Request {
  // 服务器基础路径
  baseURL: string

  constructor(params: { baseURL: string }) {
    this.baseURL = params.baseURL
  }

  /**
   * 发起请求
   *
   * @param params 请求相关参数
   * @returns
   */
  async request(params: {
    url: string
    method: string
    data?: Record<string, any>
    headers?: Record<string, any>
    await?: boolean
  }) {
    // 基础请求参数
    const url = params.url || ''
    const method = params.method || 'GET'
    const body = params.data || {}
    let headers = params.headers || {}

    headers = {
      'Content-Type': 'application/json;charset=utf-8'
    }

    const token = localStorage.getItem('token')
    if (token) {
      headers.token = token
    }

    const withBaseURL = url.indexOf('http') == 0
    // 请求路径
    const requestURL = withBaseURL ? url : this.baseURL + url

    const options: Record<string, any> = {
      method,
      headers
    }

    if (params.await) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0
      })
    }

    if (method == 'POST') {
      options.body = JSON.stringify(body)
    }

    const response = await fetch(requestURL, options)

    closeToast()

    const { code, data, msg } = await response.json()
    if (code == 401) {
      // token不能为空，也有可能失效
      // 清除本地token
      localStorage.removeItem('token')
    } else if (code == 500) {
      showToast(msg || '请求异常')
    }
    // TODO 更多错误判断
    return data
  }
}

/**
 * 解析GET请求的对象参数
 *
 * @param query 参数
 * @returns
 */
const queryParse = (query: Record<string, any>) => {
  let queryText = ''
  for (const key in query) {
    queryText += `${key}=${query[key]}&`
  }
  return queryText.slice(0, -1)
}

/** 创建请求对象 */
const request = new Request({
  baseURL: import.meta.env.VITE_APP_BASE_URL
})

export { request, queryParse }
