import { queryParse, request } from './request'

/**
 * 获取生活圈类型列表
 * @returns
 */
export function getList() {
  return request.request({
    url: '/applet/lifeCircleType/getList',
    method: 'GET'
  })
}

/**
 * 获取社区详情
 * @returns
 */
export function getDetail(params: Record<string, any>) {
  return request.request({
    url: `/applet/community/details?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 获取生活圈对应经纬度
 * @returns
 */
export function getListByType(params: Record<string, any>) {
  return request.request({
    url: `/applet/lifeCircle/getList?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 获取游线列表
 * @param params 参数
 * @returns
 */
export const getPage = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/communityTourLine/getPage?${queryParse(params)}`,
    method: 'GET',
    await: true
  })
}

/**
 * 查询游线详情
 * @param params 参数
 * @returns
 */
export const getDetails = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/communityTourLine/getDetails?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 景点详情
 * @param params 参数
 * @returns
 */
export const getAttractionsDetails = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/communityTourLine/getAttractionsDetails?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 区域列表
 * @param params 参数
 * @returns
 */
export const getDistrictByHeadChar = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/district/getDistrictByHeadChar?${queryParse(params)}`,
    method: 'GET',
    await: true
  })
}

/**
 * 查询生活圈列表
 * @param params 参数
 * @returns
 */
export const getPageList = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/lifeCircle/getPageList?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 查询导览配置
 * @param params 参数
 * @returns
 */
export const getCommunityTourConfig = (params: Record<string, any>) => {
  return request.request({
    url: `/applet/config/getCommunityTourConfig?${queryParse(params)}`,
    method: 'GET'
  })
}

/**
 * 点击添加社区导览次数
 * @return 
 */
export function addCommunityGuideUsage(params : Record<string, any>) {
	return request.request({
		url: `/cockpit/statistics/addCommunityGuideUsage`,
		method: 'POST',
		data: params
	})
}

/**
 * 获取签名配置信息
 * @return 
 */
export function getSign(params : Record<string, any>) {
	return request.request({
		url: `/applet/wechatSign/getSign`,
		method: 'POST',
		data: params
	})
}

/**
 * 添加生活圈统计次数
 * @return 
 */
export function addLifeCircleUsage(params : Record<string, any>) {
	return request.request({
		url: `/cockpit/statistics/addLifeCircleUsage`,
		method: 'POST',
		data: params
	})
}

/**
 * 添加游线统计次数
 * @return 
 */
export function addCommunityTourLineUsage(params : Record<string, any>) {
	return request.request({
		url: `/cockpit/statistics/addCommunityTourLineUsage`,
		method: 'POST',
		data: params
	})
}

/**
 * 获取所有区域街道数据
 * @param params 参数
 * @returns
 */
export function getAllDistrictStreet(params: Record<string, any> = {}) {
  return request.request({
    url: `/applet/district/getAllDistrictStreet?${queryParse(params)}`,
    method: 'GET'
  })
}