<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { getList, getPageList } from '@/common/api'

/** 父组件v-model值 */
const props = defineProps<{
  modelValue: boolean,
  /** 区域编码 */
  districtCode: string
}>()

/** 双向绑定 */
const emit = defineEmits(['update:modelValue', 'toMarkerPoint'])

/** 弹框显示状态 */
const show = ref(false)

/** 侦听弹框状态 */
watch([() => props.modelValue, show], ([newModel, newShow]) => {
  show.value = newModel
  emit('update:modelValue', newShow)
})

/** 搜索关键字 */
const keyword = ref('')
/** 对应tab索引 */
const active = ref(0)
/** 列表加载 */
const loading = ref(false)
/** 已加载完成 */
const finished = ref(false)

/** tab列表 */
const typeList = ref<Record<string, any>[]>([])
/** 数据列表 */
const dataList = ref<Record<string, any>[]>([])

/** 分页参数 */
const pageNum = ref(1)
/** 每次请求数 */
const pageSize = ref(10)

onMounted(async () => {
  await getTypeList()
  // getDataList()
})

/** tab列表获取 */
async function getTypeList() {
  const result = await getList()
  typeList.value = result

  typeList.value.unshift({
    typeName: '全部',
    typeId: ''
  })
}

/** 获取生活圈列表 */
function getDataList() {
  loading.value = true

  getPageList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    districtCode: props.districtCode,
    circleName: keyword.value,
    typeId: typeList.value[active.value].typeId
  }).then((result) => {
    loading.value = false
    if (result.records) {
      dataList.value = dataList.value.concat(result.records)
      if (result.records.length < pageSize.value) {
        finished.value = true
      } else {
        pageNum.value += 1
      }
    }
  })
}

/** 搜索 */
function search() {
  active.value = 0
  pageNum.value = 1
  finished.value = false
  dataList.value = []
  getDataList()
}

/** 切换tab */
function changeTab() {
  pageNum.value = 1
  finished.value = false
  dataList.value = []
  getDataList()
}

/** 定位到标记点 */
function _toMarkerPoint(item: Record<string, any>) {
  show.value = false
  emit('toMarkerPoint', item)
}
</script>

<template>
  <!-- 生活圈页面 -->
  <van-popup v-model:show="show" position="right" style="width: 100%; height: 100%">
    <div class="search-top-box">
      <van-icon @click="show = false" name="arrow-left" size="22" />
      <van-search
        shape="round"
        show-action
        v-model="keyword"
        placeholder="请输入周边服务点"
        @search="search()"
      >
        <template #action>
          <div @click="search()">搜索</div>
        </template>
      </van-search>
    </div>
    <van-tabs v-model:active="active" @change="changeTab" color="#2bbc4a">
      <van-tab v-for="(item, index) in typeList" :title="item.typeName" :key="index">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="getDataList"
        >
          <div
            class="service-point-list"
            v-for="(dataItem, i) in dataList"
            :key="i"
            @click="_toMarkerPoint(dataItem)"
          >
            <van-image width="60" height="60" radius="10" :src="dataItem.circleImages" />
            <div class="service-point-info">
              <div>
                <van-text-ellipsis
                  rows="1"
                  class="service-point-name"
                  :content="dataItem.circleName"
                />
                <!-- <div></div> -->
              </div>
              <van-text-ellipsis rows="1" :content="dataItem.circleAddress" />
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </van-popup>
</template>

<style lang="scss" scoped>
.search-top-box {
  display: flex;
  align-items: center;
  :deep(.van-icon) {
    margin-left: 10px;
    flex-direction: 0;
  }

  .van-search {
    width: 100%;
  }
}

:deep(.van-tabs__wrap) {
  box-shadow: 0px 6px 5px 0px rgba(169, 169, 169, 0.1);
}

:deep(.van-tab) {
  font-size: 15px;
}

.service-point-list {
  display: flex;
  margin: 0 15px;
  padding: 12px 0;
}

.service-point-list .van-image {
  margin-right: 12px;
  flex-shrink: 0;
}

.service-point-list .service-point-info {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  color: #9d9d9d;
  font-size: 15px;
}

.service-point-list .service-point-name {
  font-weight: 600;
  color: #333;
  font-size: 17px;
}

.van-text-ellipsis {
  width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.van-list {
  height: calc(100vh - 98px);
  overflow-y: auto;
}
</style>
