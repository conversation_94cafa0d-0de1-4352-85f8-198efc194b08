<script setup lang="ts">
import { getList } from '@/common/api'
import { onMounted, ref } from 'vue'

const emit = defineEmits(['click'])

/** 生活圈列表 */
const typeList = ref()

onMounted(() => {
  getList().then((result) => {
    typeList.value = result
  })
})

function _click(item: Record<string, any>) {
  if(item.children.length > 0){
    return
  }
  emit('click', item)
}
</script>

<template>
  <div class="category-list">
    <div class="category-item" v-for="(item, index) in typeList" :key="index">
      <div class="category-title">
        <span>{{ item.typeName }}</span>
      </div>

      <div class="category-child">
        <div
          class="category-child-item"
          @click="_click(child)"
          v-for="(child, i) in item.children"
          :key="i"
        >
          <div v-if="child.children.length <= 0" class="category-item-level1">
            <van-image :src="child.typeIcon" width="24" height="24" />
            <span>{{ child.typeName }}</span>
          </div>

          <div v-else class="category-list-level2">
            <div class="category-item-level2">
              <div
                class="category-title-level2"
                :style="{
                  background: child.bgColor,
                  // color: child.fontColor
                }"
              >
                <span>{{ child.typeName }}</span>
              </div>

              <div class="category-child-level2">
                <div
                  class="category-child-item-level2"
                  v-for="(child1, j) in child.children"
                  :key="j"
                  @click="_click(child1)"
                >
                  <van-image :src="child1.typeIcon" width="24" height="24" />
                  <span>{{ child1.typeName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.category-list {
  padding: 0 20px;
  height: 300px;
  overflow-y: auto;

  .category-item {
    .category-title {
      font-size: 16px;
      font-weight: 600;
      margin: 15px 0 0 6px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 4px;
        height: 20px;
        background: #2bbc4a;
        right: calc(100% + 6px);
        top: 50%;
        transform: translateY(-50%);
        border-radius: 4px;
      }
    }

    .category-child {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;

      .category-list-level2 {
        display: flex;
        flex-wrap: wrap;
        .category-item-level2 {
          .category-title-level2 {
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            background: #e9fbf3;
            padding: 4px 12px;
            color: #2cd28a;
            border-radius: 6px;
            margin-top: 15px;
          }

          .category-child-item-level2 {
            display: flex;
            align-items: center;
            font-size: 14px;
            margin-top: 15px;
            > span {
              margin-left: 6px;
            }
          }
        }
      }

      .category-child-item {
        width: 50%;
        .category-item-level1 {
          display: flex;
          align-items: center;
          margin-top: 15px;
          > span {
            margin-left: 6px;
          }
        }
      }
    }
  }
}
</style>
