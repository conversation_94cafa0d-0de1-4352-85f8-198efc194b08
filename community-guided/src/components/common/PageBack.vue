<script setup lang="ts">
const emit = defineEmits(['click'])

/** 点击图标 */
function _click() {
  emit('click')
}
</script>

<template>
  <div class="back-icon" @click="_click()">
    <van-icon name="revoke" color="#333" size="16" />
  </div>
</template>

<style lang="scss" scoped>
.back-icon {
  background: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  left: 20px;
  top: 15px;
  z-index: 100;
}
</style>
