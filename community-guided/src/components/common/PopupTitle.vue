<script setup lang="ts">
import icon10Img from '@/assets/images/icon/<EMAIL>'
import icon11Img from '@/assets/images/icon/<EMAIL>'

defineProps<{
  /** 标题 */
  title: string
}>()
</script>

<template>
  <div class="title-box">
    <van-image :src="icon10Img" width="8" height="15" />
    <div class="title-content">{{ title }}</div>
    <van-image :src="icon11Img" width="8" height="15" />
  </div>
</template>

<style lang="scss" scoped>
.title-box {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.title-box .title-content {
  padding: 0 14px;
  font-weight: bold;
  font-size: 15px;
  color: #333333;
}
</style>
