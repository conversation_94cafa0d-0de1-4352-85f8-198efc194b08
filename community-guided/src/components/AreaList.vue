<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { getDistrictByHeadChar, getAllDistrictStreet } from '@/common/api'
import wx from 'weixin-js-sdk'
import { useRoute } from 'vue-router'

/** 参数路由 */
const route = useRoute()
/** 最近选择过的区域列表 */
const history = ref<Record<string, any>[]>([])
/** 区域列表信息 */
const data = reactive<Record<string, any>>({})
/** 查询关键字 */
const keyword = ref<string>('')
/** 街道地址列表 */
const streetList = ref<any[]>([])

const nameObj = Object.freeze({
  code: "510183",
  headChar: "Q",
  latitude: 30.41029,
  longitude: 103.464176,
  name: "邛崃市",})

const emit = defineEmits(['selectArea'])

onMounted(() => {
  getHistoryArea()
  getIndexList()
  getStreetList()
})

/** 从地址栏中获取最近选择过的区域 */
function getHistoryArea() {
  const historyList = route.query.history as string
  // 存在时才赋值
  if (historyList) {
    history.value = JSON.parse(historyList)
  }
}

/** 获取区域列表 */
function getIndexList() {
  getDistrictByHeadChar({
    name: keyword.value
  }).then((result) => {
    const { district, hotCommunity, headChars } = result
    data.district = district
    data.hotCommunity = hotCommunity
    data.headChars = headChars
  })
}

/** 获取街道地址列表 */
async function getStreetList() {
  try {
    const result = await getAllDistrictStreet()
    console.log('街道地址数据类型:', typeof result, Array.isArray(result))
    console.log('街道地址数据内容:', result)
    
    // 判断result是否为数组
    if (Array.isArray(result)) {
      streetList.value = result
      console.log('已设置街道列表(数组):', streetList.value)
    } else if (result && result.data && Array.isArray(result.data)) {
      // 兼容可能的data字段包装
      streetList.value = result.data
      console.log('已设置街道列表(data字段):', streetList.value)
    }
  } catch (error) {
    console.error('获取街道地址列表失败:', error)
  }
}

/** 选择区域 */
function _selectArea(item: Record<string, any>) {
  // 发送消息给的小程序,将其保存至历史访问缓存
  wx.miniProgram.postMessage({
    data: item
  })

  emit('selectArea', item)
}

/** 选择最新社区 */
function _selectNewArea(item: Record<string, any>) {
  _selectArea({
    name: item.districtName,
    code: item.districtCode,
    longitude: item.longitude,
    latitude: item.latitude
  })
}

/** 搜索 */
function search() {
  getIndexList()
}
</script>

<template>
  <van-index-bar :index-list="data.headChars">
    <div>
      <van-search
        shape="round"
        show-action
        v-model="keyword"
        placeholder="搜索社区"
        @search="search()"
      >
        <template #action>
          <div @click="search()">搜索</div>
        </template>
      </van-search>
    </div>
    <div v-if="!keyword.trim()">
      <div class="content-box" @click="_selectArea(nameObj)">
        <div class="name-box">
          <div class="name-box-title">邛崃市(平台)</div>
          <div class="name-box-content">
            {{ nameObj.name }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 街道地址列表 -->
    <div v-if="streetList && streetList.length > 0 && !keyword.trim()">
      <div class="index-title">镇(街道)平台</div>
      <!-- <div class="index-title">街道地址</div> -->
      <div class="content-box">
        <div 
          v-for="(item, index) in streetList" 
          :key="index" 
          @click="_selectArea({
            name: item.name || '',
            code: item.code || '',
            longitude: item.longitude || '',
            latitude: item.latitude || ''
          })"
        >
          {{ item.name || '未命名街道' }}
        </div>
      </div>
    </div>
    
    <div v-if="data.hotCommunity && !keyword.trim()">
     
      <div class="index-title">最火社区</div>
      <div class="content-box">
        <div v-for="(item, index) in data.hotCommunity" :key="index" @click="_selectNewArea(item)">
          {{ item.districtName }}
        </div>
      </div>
    </div>
    <div class="index-title">村(社区)平台</div>
    <!-- <div v-if="history.length > 0">
      <div class="index-title">最近访问</div>
      <div class="content-box">
        <div v-for="(item, index) in history" :key="index" @click="_selectArea(item)">
          {{ item.name }}
        </div>
      </div>
    </div> -->
    <div v-for="(val, key) in data.district" :key="key">
      <van-index-anchor :index="key">{{ key }}</van-index-anchor>
      <div class="content-box">
        <div v-for="(item, i) in val" :key="i" @click="_selectArea(item)">
          {{ item.name }}
        </div>
      </div>
    </div>
  </van-index-bar>
</template>

<style lang="scss" scoped>
.index-title {
  height: 32px;
  padding: 0 16px;
  margin-top: 10px;
  font-size: 14px;
  line-height: 32px;
  color: #333;
  font-weight: bold;
}
.content-box {
  display: flex;
  flex-wrap: wrap;
}
.name-box {
  
}
.name-box-title {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
}
.name-box-content {
  font-size: 14px;
  color: #333;
}
.content-box > div {
  padding: 10px 20px;
  color: #666;
  font-size: 14px;
}
</style>
