import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'
import LineMapView from '@/views/LineMapView.vue'
import AreaView from '@/views/AreaView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        index: 0
      }
    },
    {
      path: '/lineMap',
      name: 'lineMap',
      component: LineMapView,
      meta: {
        index: 1
      }
    },
    {
      path: '/areaView',
      name: 'areaView',
      component: AreaView,
      meta: {
        index: 1,
        title: '辖区列表'
      }
    }
  ]
})

// beforeEach是router的钩子函数，在进入路由前执行
router.beforeEach((to, from, next) => {
  // 判断是否有标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
  // 执行进入路由，如果不写就不会进入目标页
  next()
})

export default router
