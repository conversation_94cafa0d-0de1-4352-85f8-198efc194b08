{"name": "community-guided", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "big.js": "^6.2.1", "crypto-js": "^4.2.0", "moment": "^2.29.4", "pinia": "^2.1.7", "vant": "^4.8.0", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "vue3-carousel": "^0.3.1", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/big.js": "^6.2.2", "@types/node": "^18.18.5", "@vant/auto-import-resolver": "^1.0.2", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "sass": "^1.72.0", "typescript": "~5.2.0", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.11", "vite-plugin-node-polyfills": "^0.16.0", "vue-tsc": "^1.8.19"}}